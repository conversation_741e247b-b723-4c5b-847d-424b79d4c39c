import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/core/models/models.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';
import 'package:flutter_smarthome/core/utils/hex_color.dart';

class SignatureSearchDialog extends StatefulWidget {
  final Map<String, dynamic>? initialSearchParams;
  final Function(Map<String, dynamic>) onSearchConfirmed;

  const SignatureSearchDialog({
    super.key,
    this.initialSearchParams,
    required this.onSearchConfirmed,
  });

  @override
  State<SignatureSearchDialog> createState() => _SignatureSearchDialogState();
}

class _SignatureSearchDialogState extends State<SignatureSearchDialog> {
  // 文本输入控制器
  final TextEditingController _customerCodeController = TextEditingController();
  final TextEditingController _customerNameController = TextEditingController();
  final TextEditingController _customerPhoneController =
      TextEditingController();
  final TextEditingController _referrerPhoneController =
      TextEditingController();

  // 下拉选择的值
  String? selectedDecorationTypeId;
  String? selectedDesignDeptId;
  String? selectedFirstSourceId;
  String? selectedSourceId;
  String? selectedSourceDetailId;
  String? selectedChannelUserId;

  // 下拉选项数据
  List<Map<String, dynamic>> decorationTypes = [];
  List<Map<String, dynamic>> designDepts = [];
  List<Map<String, dynamic>> firstSources = [];
  List<Map<String, dynamic>> sourceDetails = [];
  List<Map<String, dynamic>> users = [];
  List<Map<String, dynamic>> channelUsers = [];

  // 加载状态
  bool isLoadingDecorationTypes = false;
  bool isLoadingDesignDepts = false;
  bool isLoadingFirstSources = false;
  bool isLoadingSourceDetails = false;
  bool isLoadingUsers = false;
  bool isLoadingChannelUsers = false;

  @override
  void initState() {
    super.initState();
    _loadDecorationTypes();
    _loadDesignDepts();
    _loadFirstSources();
    _loadUsers();
    _loadChannelUsers();
    _initializeWithParams();
  }

  void _initializeWithParams() async {
    if (widget.initialSearchParams != null) {
      final params = widget.initialSearchParams!;
      _customerCodeController.text = params['customerCode'] ?? '';
      _customerNameController.text = params['customerName'] ?? '';
      _customerPhoneController.text = params['customerPhone'] ?? '';
      _referrerPhoneController.text = params['referrerPhone'] ?? '';
      selectedDecorationTypeId = params['decorationTypeId'];
      selectedDesignDeptId = params['designDeptId'];
      selectedFirstSourceId = params['firstSourceId'];
      selectedSourceId = params['sourceId'];
      selectedChannelUserId = params['channelUserId'];

      // 如果有来源ID，先加载来源明细，然后再设置选中的明细ID
      if (params['sourceId'] != null && params['sourceDetailId'] != null) {
        await _loadSourceDetails(params['sourceId']);
        // 加载完成后，检查明细ID是否在列表中，如果在才设置
        final detailId = params['sourceDetailId'] as String;
        final hasMatchingDetail =
            sourceDetails.any((detail) => _getIdFromOption(detail) == detailId);
        if (hasMatchingDetail) {
          setState(() {
            selectedSourceDetailId = detailId;
          });
        }
      }
    }
  }

  // 加载装修类型
  Future<void> _loadDecorationTypes() async {
    setState(() {
      isLoadingDecorationTypes = true;
    });
    try {
      await _fetchDecorationTypes('crm_decorate_type');
    } catch (e) {
      print('加载装修类型失败: $e');
    } finally {
      setState(() {
        isLoadingDecorationTypes = false;
      });
    }
  }

  // 加载设计部门
  Future<void> _loadDesignDepts() async {
    setState(() {
      isLoadingDesignDepts = true;
    });
    try {
      await _fetchDesignDepts();
    } catch (e) {
      print('加载设计部门失败: $e');
    } finally {
      setState(() {
        isLoadingDesignDepts = false;
      });
    }
  }

  // 加载首次来源
  Future<void> _loadFirstSources() async {
    setState(() {
      isLoadingFirstSources = true;
    });
    try {
      await _fetchSources();
    } catch (e) {
      print('加载首次来源失败: $e');
    } finally {
      setState(() {
        isLoadingFirstSources = false;
      });
    }
  }

  // 加载来源明细
  Future<void> _loadSourceDetails(String sourceId) async {
    setState(() {
      isLoadingSourceDetails = true;
      // 立即清空来源明细数据和选中值，避免状态不一致
      sourceDetails.clear();
      selectedSourceDetailId = null;
    });
    try {
      await _fetchSourceDetails(sourceId);
    } catch (e) {
      print('加载来源明细失败: $e');
      // 加载失败时确保数据清空
      setState(() {
        sourceDetails.clear();
        selectedSourceDetailId = null;
      });
    } finally {
      setState(() {
        isLoadingSourceDetails = false;
      });
    }
  }

  // 加载用户列表
  Future<void> _loadUsers() async {
    setState(() {
      isLoadingUsers = true;
    });
    try {
      await _fetchUsers();
    } catch (e) {
      print('加载用户列表失败: $e');
    } finally {
      setState(() {
        isLoadingUsers = false;
      });
    }
  }

  // 加载渠道客服（复用用户接口）
  Future<void> _loadChannelUsers() async {
    setState(() {
      isLoadingChannelUsers = true;
    });
    try {
      await _fetchUsers();
      // 将用户数据复制到渠道客服列表，并进行去重处理
      final validChannelUsers = <Map<String, dynamic>>[];
      final seenIds = <String>{};

      for (final user in users) {
        final id = _getIdFromOption(user);
        final name = _getNameFromOption(user);

        // 只添加有效的数据项（必须有ID和名称），并且ID不能重复
        if (id.isNotEmpty && name.isNotEmpty && !seenIds.contains(id)) {
          seenIds.add(id);
          validChannelUsers.add(user);
        }
      }

      channelUsers = validChannelUsers;
    } catch (e) {
      print('加载渠道客服失败: $e');
    } finally {
      setState(() {
        isLoadingChannelUsers = false;
      });
    }
  }

  @override
  void dispose() {
    _customerCodeController.dispose();
    _customerNameController.dispose();
    _customerPhoneController.dispose();
    _referrerPhoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.9,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r),
          topRight: Radius.circular(16.r),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 拖拽指示器
            Container(
              margin: EdgeInsets.only(top: 8.h, bottom: 4.h),
              width: 36.w,
              height: 4.h,
              decoration: BoxDecoration(
                color: HexColor('#E0E0E0'),
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),

            // 标题栏
            Container(
              height: 50.h,
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Stack(
                children: [
                  // 居中的标题
                  Center(
                    child: Text(
                      '查询',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  // 右侧的关闭按钮
                  Positioned(
                    right: 0,
                    top: 0,
                    bottom: 0,
                    child: GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: Icon(
                        Icons.close,
                        size: 24.w,
                        color: HexColor('#999999'),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 表单内容
            Flexible(
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Column(
                  children: [
                    SizedBox(height: 16.h),

                    // 客户编号
                    _buildInputField('客户编号', _customerCodeController, '请输入'),

                    SizedBox(height: 16.h),

                    // 装修类型
                    _buildApiDropdownField('装修类型', selectedDecorationTypeId,
                        decorationTypes, isLoadingDecorationTypes, (value) {
                      setState(() {
                        selectedDecorationTypeId = value;
                      });
                    }),

                    SizedBox(height: 16.h),

                    // 客户姓名
                    _buildInputField('客户姓名', _customerNameController, '请输入'),

                    SizedBox(height: 16.h),

                    // 客户电话
                    _buildInputField('客户电话', _customerPhoneController, '请输入'),

                    SizedBox(height: 16.h),

                    // 设计部门
                    _buildApiDropdownField('设计部门', selectedDesignDeptId,
                        designDepts, isLoadingDesignDepts, (value) {
                      setState(() {
                        selectedDesignDeptId = value;
                      });
                    }),

                    SizedBox(height: 16.h),

                    // 首次来源
                    _buildApiDropdownField('首次来源', selectedFirstSourceId,
                        firstSources, isLoadingFirstSources, (value) {
                      setState(() {
                        selectedFirstSourceId = value;
                      });
                    }),

                    SizedBox(height: 16.h),

                    // 来源
                    _buildApiDropdownField('来源', selectedSourceId, firstSources,
                        isLoadingFirstSources, (value) {
                      _handleSourceChange(value);
                    }),

                    SizedBox(height: 16.h),

                    // 来源明细
                    _buildApiDropdownField('来源明细', selectedSourceDetailId,
                        sourceDetails, isLoadingSourceDetails, (value) {
                      setState(() {
                        selectedSourceDetailId = value;
                      });
                    }, enabled: selectedSourceId != null),

                    SizedBox(height: 16.h),

                    // 渠道客服
                    _buildApiDropdownField('渠道客服', selectedChannelUserId,
                        channelUsers, isLoadingChannelUsers, (value) {
                      setState(() {
                        selectedChannelUserId = value;
                      });
                    }),

                    SizedBox(height: 16.h),

                    // 推荐人手机号
                    _buildInputField('推荐人手机号', _referrerPhoneController, '请输入'),

                    SizedBox(height: 32.h),
                  ],
                ),
              ),
            ),

            // 底部按钮
            Container(
              padding: EdgeInsets.all(16.w),
              child: Row(
                children: [
                  // 重置按钮
                  Expanded(
                    child: GestureDetector(
                      onTap: _resetForm,
                      child: Container(
                        height: 44.h,
                        decoration: BoxDecoration(
                          color: HexColor('#F5F5F5'),
                          borderRadius: BorderRadius.circular(22.r),
                        ),
                        child: Center(
                          child: Text(
                            '重置',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: HexColor('#666666'),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),

                  SizedBox(width: 12.w),

                  // 查询按钮
                  Expanded(
                    child: GestureDetector(
                      onTap: _confirmSearch,
                      child: Container(
                        height: 44.h,
                        decoration: BoxDecoration(
                          color: Colors.black,
                          borderRadius: BorderRadius.circular(22.r),
                        ),
                        child: Center(
                          child: Text(
                            '查询',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建输入框字段
  Widget _buildInputField(
      String label, TextEditingController controller, String hint) {
    return Row(
      children: [
        SizedBox(
          width: 80.w,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.black,
            ),
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Container(
            height: 40.h,
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: HexColor('#E0E0E0')),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: TextField(
              controller: controller,
              style: TextStyle(fontSize: 14.sp),
              textAlignVertical: TextAlignVertical.center,
              decoration: InputDecoration(
                hintText: hint,
                hintStyle: TextStyle(
                  fontSize: 14.sp,
                  color: HexColor('#CCCCCC'),
                ),
                border: InputBorder.none,
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
                isDense: true,
              ),
            ),
          ),
        ),
      ],
    );
  }

  // 构建API数据的下拉选择字段
  Widget _buildApiDropdownField(
    String label,
    String? selectedId,
    List<Map<String, dynamic>> options,
    bool isLoading,
    Function(String?) onChanged, {
    bool enabled = true,
  }) {
    return Row(
      children: [
        SizedBox(
          width: 80.w,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.black,
            ),
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: Container(
            height: 40.h,
            decoration: BoxDecoration(
              color: enabled ? Colors.white : HexColor('#F5F5F5'),
              border: Border.all(color: HexColor('#E0E0E0')),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: isLoading
                ? Center(
                    child: SizedBox(
                      width: 16.w,
                      height: 16.w,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: HexColor('#999999'),
                      ),
                    ),
                  )
                : Stack(
                    children: [
                      // 下拉框
                      DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          value: _getValidatedValue(selectedId, options),
                          hint: Padding(
                            padding: EdgeInsets.only(
                              left: 12.w,
                              right: 12.w, // 默认状态下的右边距
                            ),
                            child: Text(
                              '请选择',
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: enabled
                                    ? HexColor('#CCCCCC')
                                    : HexColor('#999999'),
                              ),
                            ),
                          ),
                          isExpanded: true,
                          icon: Padding(
                            padding: EdgeInsets.only(
                              right: selectedId != null && enabled
                                  ? 40.w
                                  : 12.w, // 动态调整下拉箭头位置
                            ),
                            child: Icon(
                              Icons.keyboard_arrow_down,
                              color: enabled
                                  ? HexColor('#999999')
                                  : HexColor('#CCCCCC'),
                              size: 20.w,
                            ),
                          ),
                          items: enabled && options.isNotEmpty
                              ? options
                                  .map((Map<String, dynamic> option) {
                                    final id = _getIdFromOption(option);
                                    final name = _getNameFromOption(option);
                                    // 确保每个item都有有效的id和name
                                    if (id.isEmpty || name.isEmpty) {
                                      return null;
                                    }
                                    return DropdownMenuItem<String>(
                                      value: id,
                                      child: Padding(
                                        padding: EdgeInsets.only(left: 12.w),
                                        child: Text(
                                          name,
                                          style: TextStyle(fontSize: 14.sp),
                                        ),
                                      ),
                                    );
                                  })
                                  .where((item) => item != null)
                                  .cast<DropdownMenuItem<String>>()
                                  .toList()
                              : null,
                          onChanged: enabled ? onChanged : null,
                          dropdownColor: Colors.white,
                          selectedItemBuilder: enabled && options.isNotEmpty
                              ? (BuildContext context) {
                                  return options
                                      .map((Map<String, dynamic> option) {
                                    final name = _getNameFromOption(option);
                                    return Padding(
                                      padding: EdgeInsets.only(
                                        left: 12.w,
                                        right: selectedId != null && enabled
                                            ? 40.w
                                            : 12.w, // 动态调整选中文本的右边距
                                      ),
                                      child: Align(
                                        alignment: Alignment.centerLeft,
                                        child: Text(
                                          name,
                                          style: TextStyle(
                                            fontSize: 14.sp,
                                            color: Colors.black,
                                          ),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    );
                                  }).toList();
                                }
                              : null,
                        ),
                      ),

                      // 重置图标 - 只有在有选中值且启用状态下才显示
                      if (enabled && selectedId != null)
                        Positioned(
                          right: 12.w,
                          top: 0,
                          bottom: 0,
                          child: GestureDetector(
                            onTap: () {
                              // 重置该项的选择
                              onChanged(null);
                            },
                            child: Container(
                              width: 24.w,
                              height: 24.w,
                              decoration: BoxDecoration(
                                color: HexColor('#F5F5F5'),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.close,
                                size: 14.w,
                                color: HexColor('#999999'),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
          ),
        ),
      ],
    );
  }

  // 从选项中获取ID
  String _getIdFromOption(Map<String, dynamic> option) {
    // 对于来源明细数据，优先使用activityId作为唯一标识
    if (option.containsKey('activityId') &&
        option.containsKey('activityName')) {
      return option['activityId'] ?? '';
    }

    return option['sourceId'] ?? // 来源数据
        option['deptId'] ?? // 部门数据
        option['userId'] ?? // 人员数据
        option['value'] ?? // 装修类型等字典数据的value字段
        option['dictId'] ?? // 装修类型等字典数据的dictId字段
        option['id'] ?? // 通用字段
        '';
  }

  // 从选项中获取名称
  String _getNameFromOption(Map<String, dynamic> option) {
    return option['sourceName'] ?? // 来源数据
        option['activityName'] ?? // 来源明细数据
        option['realName'] ?? // 人员数据（优先级提高）
        option['deptName'] ?? // 部门数据
        option['label'] ?? // 装修类型等字典数据的label字段
        option['text'] ?? // 装修类型等字典数据的text字段
        option['dictName'] ?? // 装修类型等字典数据的dictName字段
        option['name'] ?? // 通用名称字段
        '';
  }

  // 验证选中的值是否在选项列表中
  String? _getValidatedValue(
      String? selectedValue, List<Map<String, dynamic>> options) {
    if (selectedValue == null || options.isEmpty) {
      return null;
    }

    final hasMatchingOption =
        options.any((option) => _getIdFromOption(option) == selectedValue);

    // 如果值不匹配，立即返回null，避免DropdownButton错误
    if (!hasMatchingOption) {
      // 针对来源明细的特殊处理，异步清空选中值
      if (options == sourceDetails) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted && selectedSourceDetailId == selectedValue) {
            setState(() {
              selectedSourceDetailId = null;
            });
          }
        });
      }
      return null;
    }

    return selectedValue;
  }

  // 处理来源选择变化
  void _handleSourceChange(String? value) {
    // 选择来源后，异步加载来源明细
    if (value != null && value != selectedSourceId) {
      setState(() {
        selectedSourceId = value;
        // 立即清空来源明细选择和数据，避免状态不一致
        selectedSourceDetailId = null;
        sourceDetails.clear();
      });
      _loadSourceDetails(value);
    } else if (value == null) {
      setState(() {
        selectedSourceId = null;
        selectedSourceDetailId = null;
        sourceDetails.clear();
      });
    }
  }

  // 重置表单
  void _resetForm() {
    setState(() {
      _customerCodeController.clear();
      _customerNameController.clear();
      _customerPhoneController.clear();
      _referrerPhoneController.clear();
      selectedDecorationTypeId = null;
      selectedDesignDeptId = null;
      selectedFirstSourceId = null;
      selectedSourceId = null;
      selectedSourceDetailId = null;
      selectedChannelUserId = null;
      // 清空来源明细列表
      sourceDetails.clear();
    });
  }

  //获取装修类型
  Future<void> _fetchDecorationTypes(String path) async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/home/<USER>/$path',
        queryParameters: null,
      );

      if (response != null) {
        print('装修类型API响应: $response'); // 调试信息
        // 处理多种可能的响应格式
        List<dynamic>? decorationTypeData;

        if (response is Map<String, dynamic>) {
          // 尝试多个可能的键名
          decorationTypeData =
              response['pbs_decoration_type'] as List<dynamic>? ??
                  response['crm_decorate_type'] as List<dynamic>? ??
                  response['data'] as List<dynamic>? ??
                  response['list'] as List<dynamic>?;
        } else if (response is List<dynamic>) {
          decorationTypeData = response;
        }

        if (decorationTypeData != null && decorationTypeData.isNotEmpty) {
          decorationTypes = decorationTypeData
              .map((e) => Map<String, dynamic>.from(e))
              .toList();
          print('装修类型数据处理成功: $decorationTypes'); // 调试信息
        } else {
          print('装修类型数据为空或格式不正确');
          decorationTypes = [];
        }
        setState(() {});
      }
    } catch (e) {
      print('获取装修类型失败: $e');
      decorationTypes = [];
      setState(() {});
    }
  }

  //获取来源/首次来源
  Future<void> _fetchSources() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/source/list',
        queryParameters: null,
      );

      if (response != null && response is List) {
        firstSources =
            (response).map((e) => Map<String, dynamic>.from(e)).toList();
        setState(() {});
      }
    } catch (e) {
      print('获取来源失败: $e');
    }
  }

  //获取来源明细
  Future<void> _fetchSourceDetails(String sourceId) async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/source/detail',
        queryParameters: {'sourceId': sourceId},
      );

      print('来源明细API响应: $response'); // 调试信息

      if (response != null) {
        List<dynamic>? detailData;

        if (response is List<dynamic>) {
          detailData = response;
        } else if (response is Map<String, dynamic>) {
          // 尝试从可能的键中获取数据
          detailData = response['data'] as List<dynamic>? ??
              response['list'] as List<dynamic>? ??
              response['details'] as List<dynamic>?;
        }

        if (detailData != null && detailData.isNotEmpty) {
          // 确保数据格式正确，并验证每个项目都有必要的字段
          final validDetails = <Map<String, dynamic>>[];
          final seenIds = <String>{};

          for (final item in detailData) {
            if (item is Map<String, dynamic>) {
              final detail = Map<String, dynamic>.from(item);
              final id = _getIdFromOption(detail);
              final name = _getNameFromOption(detail);

              // 只添加有效的数据项（必须有ID和名称），并且ID不能重复
              if (id.isNotEmpty && name.isNotEmpty && !seenIds.contains(id)) {
                seenIds.add(id);
                validDetails.add(detail);
              }
            }
          }

          sourceDetails = validDetails;
          print('来源明细数据处理成功: $sourceDetails'); // 调试信息

          // 由于在_loadSourceDetails中已经清空了selectedSourceDetailId，这里不需要再次验证
        } else {
          print('来源明细数据为空或格式不正确');
          sourceDetails = [];
        }
        setState(() {});
      } else {
        sourceDetails = [];
        setState(() {});
      }
    } catch (e) {
      print('获取来源明细失败: $e');
      sourceDetails = [];
      setState(() {});
    }
  }

  //获取部门
  Future<void> _fetchDesignDepts() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/department/list',
        queryParameters: {'type': '2'},
      );

      if (response != null && response is List) {
        designDepts =
            (response).map((e) => Map<String, dynamic>.from(e)).toList();
        setState(() {});
      }
    } catch (e) {
      print('获取部门失败: $e');
    }
  }

  //获取人员
  Future<void> _fetchUsers() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/signature/user/list',
        queryParameters: null,
      );

      if (response != null && response is List) {
        // 对用户数据进行去重处理
        final validUsers = <Map<String, dynamic>>[];
        final seenIds = <String>{};

        for (final item in response) {
          if (item is Map<String, dynamic>) {
            final user = Map<String, dynamic>.from(item);
            final id = _getIdFromOption(user);
            final name = _getNameFromOption(user);

            // 只添加有效的数据项（必须有ID和名称），并且ID不能重复
            if (id.isNotEmpty && name.isNotEmpty && !seenIds.contains(id)) {
              seenIds.add(id);
              validUsers.add(user);
            }
          }
        }

        users = validUsers;
        setState(() {});
      }
    } catch (e) {
      print('获取人员失败: $e');
    }
  }

  // 确认查询
  void _confirmSearch() {
    final searchParams = <String, dynamic>{};

    // 输入框的值
    if (_customerCodeController.text.isNotEmpty) {
      searchParams['customerCode'] = _customerCodeController.text;
    }
    if (_customerNameController.text.isNotEmpty) {
      searchParams['customerName'] = _customerNameController.text;
    }
    if (_customerPhoneController.text.isNotEmpty) {
      searchParams['customerPhone'] = _customerPhoneController.text;
    }
    if (_referrerPhoneController.text.isNotEmpty) {
      searchParams['referrerPhone'] = _referrerPhoneController.text;
    }

    // 下拉选择的ID值
    if (selectedDecorationTypeId != null) {
      searchParams['decorationTypeId'] = selectedDecorationTypeId;
    }
    if (selectedDesignDeptId != null) {
      searchParams['designDeptId'] = selectedDesignDeptId;
    }
    if (selectedFirstSourceId != null) {
      searchParams['firstSourceId'] = selectedFirstSourceId;
    }
    if (selectedSourceId != null) {
      searchParams['sourceId'] = selectedSourceId;
    }
    if (selectedSourceDetailId != null) {
      searchParams['sourceDetailId'] = selectedSourceDetailId;
    }
    if (selectedChannelUserId != null) {
      searchParams['channelUserId'] = selectedChannelUserId;
    }

    widget.onSearchConfirmed(searchParams);
    Navigator.of(context).pop();
  }
}
