import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smarthome/core/utils/hex_color.dart';
import 'package:flutter_smarthome/core/network/api_manager.dart';
import 'package:flutter_smarthome/core/models/models.dart';
import 'package:oktoast/oktoast.dart';

class CustomerDockingDialog extends StatefulWidget {
  final String? serviceId;
  final String? serviceStatus;
  final String? decorateType;
  final String customerName;
  final String decorationAddress;
  final Function(
      {required DateTime selectedDate,
      required String intentionType,
      required String decorationType})? onConfirm;

  const CustomerDockingDialog({
    super.key,
    this.serviceId,
    required this.customerName,
    required this.decorationAddress,
    this.serviceStatus,
    this.decorateType,
    this.onConfirm,
  });

  static Future<void> show(
    BuildContext context, {
    required String customerName,
    required String decorationAddress,
    Function(
            {required DateTime selectedDate,
            required String intentionType,
            required String decorationType})?
        onConfirm,
    String? serviceId,
    String? serviceStatus,
    String? decorateType,
  }) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      isDismissible: false,
      enableDrag: false,
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: CustomerDockingDialog(
          customerName: customerName,
          decorationAddress: decorationAddress,
          onConfirm: onConfirm,
          serviceId: serviceId,
          serviceStatus: serviceStatus,
          decorateType: decorateType,
        ),
      ),
    );
  }

  @override
  State<CustomerDockingDialog> createState() => _CustomerDockingDialogState();
}

class _CustomerDockingDialogState extends State<CustomerDockingDialog> {
  DateTime selectedDate = DateTime.now();
  String selectedIntentionType = '';
  String selectedDecorationType = '';

  // API数据列表
  List<DictOption> _intentionTypeList = [];
  List<DictOption> _decorationTypeList = [];

  @override
  void initState() {
    super.initState();

    // 调试信息：检查传入的参数
    print('CustomerDockingDialog - 传入的客户姓名: ${widget.customerName}');
    print('CustomerDockingDialog - 传入的装修地址: ${widget.decorationAddress}');
    print('CustomerDockingDialog - 传入的服务状态value: ${widget.serviceStatus}');
    print('CustomerDockingDialog - 传入的装修类型value: ${widget.decorateType}');

    // 获取API数据，然后再设置默认值
    _fetchIntentionTypes();
    _fetchDecorationTypes();
  }

  // 获取意向类型数据
  Future<void> _fetchIntentionTypes() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/home/<USER>/customer_info_now_status',
        queryParameters: null,
      );

      if (response != null) {
        _intentionTypeList =
            (response['customer_info_now_status'] as List<dynamic>)
                .map((e) => DictOption.fromJson(Map<String, dynamic>.from(e)))
                .toList();

        // 根据传入的 serviceStatus value 值匹配对应的 label
        if (widget.serviceStatus != null && widget.serviceStatus!.isNotEmpty) {
          final matchedOption = _intentionTypeList.firstWhere(
            (option) => option.value == widget.serviceStatus,
            orElse: () => _intentionTypeList.isNotEmpty
                ? _intentionTypeList.first
                : DictOption(id: '', value: '', label: ''),
          );
          selectedIntentionType = matchedOption.label;
          print(
              '匹配到的意向类型 - value: ${widget.serviceStatus}, label: ${matchedOption.label}');
        } else if (selectedIntentionType.isEmpty &&
            _intentionTypeList.isNotEmpty) {
          // 如果没有预设值，设置第一个为默认值
          selectedIntentionType = _intentionTypeList.first.label;
        }

        setState(() {});
      }
    } catch (e) {
      print('获取意向类型失败: $e');
    }
  }

  // 获取装修类型数据
  Future<void> _fetchDecorationTypes() async {
    try {
      final apiManager = ApiManager();
      final response = await apiManager.get(
        '/api/home/<USER>/pbs_decoration_type',
        queryParameters: null,
      );

      if (response != null) {
        _decorationTypeList = (response['pbs_decoration_type'] as List<dynamic>)
            .map((e) => DictOption.fromJson(Map<String, dynamic>.from(e)))
            .toList();

        // 根据传入的 decorateType value 值匹配对应的 label
        if (widget.decorateType != null && widget.decorateType!.isNotEmpty) {
          final matchedOption = _decorationTypeList.firstWhere(
            (option) => option.value == widget.decorateType,
            orElse: () => _decorationTypeList.isNotEmpty
                ? _decorationTypeList.first
                : DictOption(id: '', value: '', label: ''),
          );
          selectedDecorationType = matchedOption.label;
          print(
              '匹配到的装修类型 - value: ${widget.decorateType}, label: ${matchedOption.label}');
        } else if (selectedDecorationType.isEmpty &&
            _decorationTypeList.isNotEmpty) {
          // 如果没有预设值，设置第一个为默认值
          selectedDecorationType = _decorationTypeList.first.label;
        }

        setState(() {});
      }
    } catch (e) {
      print('获取装修类型失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16.r),
          topRight: Radius.circular(16.r),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 36.w,
              height: 4.h,
              margin: EdgeInsets.only(top: 8.h),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
            _buildHeader(),
            _buildContent(),
            _buildBottomButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: HexColor('#F5F5F5'),
            width: 1,
          ),
        ),
      ),
      child: Stack(
        children: [
          // 标题居中
          Center(
            child: Text(
              '客户对接',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
          ),
          // 关闭按钮右对齐
          Positioned(
            right: 0,
            top: 0,
            bottom: 0,
            child: Center(
              child: GestureDetector(
                onTap: () => Navigator.pop(context),
                child: Icon(
                  Icons.close,
                  size: 24.w,
                  color: Colors.grey[600],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: EdgeInsets.all(20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoRow('客户姓名：', widget.customerName),
          SizedBox(height: 16.h),
          _buildAddressRow('装修地址：', widget.decorationAddress),
          SizedBox(height: 16.h),
          _buildDatePicker(),
          SizedBox(height: 16.h),
          _buildIntentionTypeSelector(),
          SizedBox(height: 16.h),
          _buildDecorationTypeSelector(),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Container(
      height: 44.h,
      padding: EdgeInsets.symmetric(horizontal: 12.w),
      decoration: BoxDecoration(
        color: HexColor('#F4F5F7'),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 80.w,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                color: HexColor('#333333'),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              textAlign: TextAlign.right,
              style: TextStyle(
                fontSize: 14.sp,
                color: HexColor('#666666'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddressRow(String label, String address) {
    return Container(
      height: 44.h,
      padding: EdgeInsets.symmetric(horizontal: 12.w),
      decoration: BoxDecoration(
        color: HexColor('#F4F5F7'),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 80.w,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                color: HexColor('#333333'),
              ),
            ),
          ),
          Expanded(
            child: Text(
              address,
              textAlign: TextAlign.right,
              style: TextStyle(
                fontSize: 14.sp,
                color: HexColor('#666666'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDatePicker() {
    return GestureDetector(
      onTap: () => _showDatePicker('选择对接日期', (selectedDateString) {
        setState(() {
          List<String> dateParts = selectedDateString.split('-');
          if (dateParts.length == 3) {
            int year = int.parse(dateParts[0]);
            int month = int.parse(dateParts[1]);
            int day = int.parse(dateParts[2]);
            selectedDate = DateTime(year, month, day);
          }
        });
      }),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4.r),
          border: Border.all(color: Colors.black),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(
              Icons.calendar_month,
              size: 20.w,
              color: Colors.black,
            ),
            SizedBox(width: 8.w),
            Text(
              '${selectedDate.year}-${selectedDate.month.toString().padLeft(2, '0')}-${selectedDate.day.toString().padLeft(2, '0')}',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.black,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIntentionTypeSelector() {
    return GestureDetector(
      onTap: () => _showPickerBottomSheet(
        '选择客户类型',
        _intentionTypeList.map((option) => option.label).toList(),
        selectedIntentionType,
        (selected) {
          setState(() {
            selectedIntentionType = selected;
          });
        },
      ),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4.r),
          border: Border.all(color: Colors.black),
        ),
        child: Center(
          child: Text(
            selectedIntentionType.isEmpty ? '请选择' : selectedIntentionType,
            style: TextStyle(
              fontSize: 14.sp,
              color: selectedIntentionType.isEmpty
                  ? Colors.grey[400]
                  : Colors.black,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDecorationTypeSelector() {
    return GestureDetector(
      onTap: () => _showPickerBottomSheet(
        '选择装修类型',
        _decorationTypeList.map((option) => option.label).toList(),
        selectedDecorationType,
        (selected) {
          setState(() {
            selectedDecorationType = selected;
          });
        },
      ),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4.r),
          border: Border.all(color: Colors.black),
        ),
        child: Column(
          children: [
            Text(
              selectedDecorationType.isEmpty ? '请选择' : selectedDecorationType,
              style: TextStyle(
                fontSize: 14.sp,
                color: selectedDecorationType.isEmpty
                    ? Colors.grey[400]
                    : Colors.black,
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              '对接后不可修改整装修类型!请慎重选择',
              style: TextStyle(
                fontSize: 12.sp,
                color: HexColor('#FF4444'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomButtons() {
    return Container(
      padding: EdgeInsets.all(20.w),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () => Navigator.pop(context),
              child: Container(
                height: 44.h,
                decoration: BoxDecoration(
                  color: HexColor('#F5F5F5'),
                  borderRadius: BorderRadius.circular(22.r),
                ),
                child: Center(
                  child: Text(
                    '取消',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: HexColor('#666666'),
                    ),
                  ),
                ),
              ),
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: GestureDetector(
              onTap: _onConfirm,
              child: Container(
                height: 44.h,
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(22.r),
                ),
                child: Center(
                  child: Text(
                    '确认',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _onConfirm() async {
    // 先调用提交客户对接接口
    await _submitCustomerDocking();

    if (widget.onConfirm != null) {
      // 根据选中的label找到对应的value
      String intentionTypeValue = '';
      String decorationTypeValue = '';

      // 找到意向类型对应的value
      final intentionOption = _intentionTypeList.firstWhere(
        (option) => option.label == selectedIntentionType,
        orElse: () => DictOption(id: '', value: '', label: ''),
      );
      intentionTypeValue = intentionOption.value;

      // 找到装修类型对应的value
      final decorationOption = _decorationTypeList.firstWhere(
        (option) => option.label == selectedDecorationType,
        orElse: () => DictOption(id: '', value: '', label: ''),
      );
      decorationTypeValue = decorationOption.value;

      print(
          '确认选择 - 意向类型value: $intentionTypeValue, 装修类型value: $decorationTypeValue');

      widget.onConfirm!(
        selectedDate: selectedDate,
        intentionType: intentionTypeValue,
        decorationType: decorationTypeValue,
      );
    }
    Navigator.pop(context);
  }

  // 显示日期选择器
  void _showDatePicker(String title, Function(String) onDateSelected) {
    // 解析当前日期，如果没有则使用今天
    DateTime currentSelectedDate = selectedDate;

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 300.h,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(12.r)),
        ),
        child: Column(
          children: [
            // 拖拽指示器
            Container(
              width: 36.w,
              height: 4.h,
              margin: EdgeInsets.only(top: 8.h),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),

            // 标题栏
            Container(
              height: 50.h,
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: Colors.grey[200]!,
                    width: 1.h,
                  ),
                ),
              ),
              child: Row(
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      '取消',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 16.sp,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      title,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                      String formattedDate =
                          '${currentSelectedDate.year}-${currentSelectedDate.month.toString().padLeft(2, '0')}-${currentSelectedDate.day.toString().padLeft(2, '0')}';
                      onDateSelected(formattedDate);
                    },
                    child: Text(
                      '确定',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 日期选择器
            Expanded(
              child: CupertinoDatePicker(
                mode: CupertinoDatePickerMode.date,
                initialDateTime: currentSelectedDate,
                minimumDate: DateTime(2020),
                maximumDate: DateTime(2030),
                onDateTimeChanged: (DateTime newDate) {
                  currentSelectedDate = newDate;
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 显示选择器底推框
  void _showPickerBottomSheet(String title, List<String> options,
      String currentValue, Function(String) onSelected) {
    int selectedIndex = options.indexOf(currentValue);
    if (selectedIndex == -1) selectedIndex = 0;

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: 300.h,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(12.r)),
        ),
        child: Column(
          children: [
            // 拖拽指示器
            Container(
              width: 36.w,
              height: 4.h,
              margin: EdgeInsets.only(top: 8.h),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),

            // 标题栏
            Container(
              height: 50.h,
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: Colors.grey[200]!,
                    width: 1.h,
                  ),
                ),
              ),
              child: Row(
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: Text(
                      '取消',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 16.sp,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      title,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context);
                      onSelected(options[selectedIndex]);
                    },
                    child: Text(
                      '确定',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 选择器
            Expanded(
              child: CupertinoPicker(
                itemExtent: 44.h,
                scrollController:
                    FixedExtentScrollController(initialItem: selectedIndex),
                onSelectedItemChanged: (index) {
                  selectedIndex = index;
                },
                children: options
                    .map(
                      (option) => Center(
                        child: Text(
                          option,
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    )
                    .toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  //提交客户端对接
  Future<void> _submitCustomerDocking() async {
    try {
      // 根据选中的label找到对应的value
      String intentionTypeValue = '';
      String decorationTypeValue = '';

      // 找到意向类型对应的value
      final intentionOption = _intentionTypeList.firstWhere(
        (option) => option.label == selectedIntentionType,
        orElse: () => DictOption(id: '', value: '', label: ''),
      );
      intentionTypeValue = intentionOption.value;

      // 找到装修类型对应的value
      final decorationOption = _decorationTypeList.firstWhere(
        (option) => option.label == selectedDecorationType,
        orElse: () => DictOption(id: '', value: '', label: ''),
      );
      decorationTypeValue = decorationOption.value;

      // 格式化日期为 YYYY-MM-DD 格式
      String formattedDate =
          '${selectedDate.year}-${selectedDate.month.toString().padLeft(2, '0')}-${selectedDate.day.toString().padLeft(2, '0')}';

      final apiManager = ApiManager();
      final response = await apiManager.post(
        '/api/signature/customer/commit',
        data: {
          'serviceId': widget.serviceId,
          'nowStatus': intentionTypeValue,
          'buttJointTime': formattedDate,
          'decorationType': decorationTypeValue,
        },
      );

      if (response != null && mounted) {
        if (response['code'] == 200) {
          widget.onConfirm!(
            selectedDate: selectedDate,
            intentionType: intentionTypeValue,
            decorationType: decorationTypeValue,
          );
          Navigator.pop(context);
        }
        showToast('客户对接提交成功', position: ToastPosition.center);
      } else {
        showToast('客户对接提交失败', position: ToastPosition.center);
      }
    } catch (e) {
      showToast('客户对接提交失败，请稍后重试', position: ToastPosition.center);
    }
  }
}
