import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import '../../helpers/test_helpers.dart';

void main() {
  group('Shared Components Tests', () {
    group('Custom Button Tests', () {
      testWidgets('should display button with text', (WidgetTester tester) async {
        const buttonText = '点击按钮';
        bool wasPressed = false;

        final widget = TestHelpers.createTestWidget(
          ElevatedButton(
            onPressed: () {
              wasPressed = true;
            },
            child: const Text(buttonText),
          ),
        );

        await tester.pumpWidget(widget);

        expect(find.text(buttonText), findsOneWidget);
        expect(find.byType(ElevatedButton), findsOneWidget);

        // 测试按钮点击
        await tester.tap(find.byType(ElevatedButton));
        await tester.pump();

        expect(wasPressed, isTrue);
      });

      testWidgets('should handle disabled state', (WidgetTester tester) async {
        final widget = TestHelpers.createTestWidget(
          ElevatedButton(
            onPressed: null, // 禁用状态
            child: const Text('禁用按钮'),
          ),
        );

        await tester.pumpWidget(widget);

        final button = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
        expect(button.onPressed, isNull);
        expect(find.text('禁用按钮'), findsOneWidget);
      });

      testWidgets('should display loading state', (WidgetTester tester) async {
        bool isLoading = true;

        final widget = TestHelpers.createTestWidget(
          StatefulBuilder(
            builder: (context, setState) {
              return ElevatedButton(
                onPressed: isLoading ? null : () {},
                child: isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('提交'),
              );
            },
          ),
        );

        await tester.pumpWidget(widget);

        expect(find.byType(CircularProgressIndicator), findsOneWidget);
        expect(find.text('提交'), findsNothing);
      });
    });

    group('Custom Input Field Tests', () {
      testWidgets('should display input field with label', (WidgetTester tester) async {
        final controller = TextEditingController();

        final widget = TestHelpers.createTestWidget(
          TextFormField(
            controller: controller,
            decoration: const InputDecoration(
              labelText: '用户名',
              hintText: '请输入用户名',
              border: OutlineInputBorder(),
            ),
          ),
        );

        await tester.pumpWidget(widget);

        expect(find.text('用户名'), findsOneWidget);
        expect(find.text('请输入用户名'), findsOneWidget);
        expect(find.byType(TextFormField), findsOneWidget);
      });

      testWidgets('should validate input', (WidgetTester tester) async {
        final formKey = GlobalKey<FormState>();
        final controller = TextEditingController();

        final widget = TestHelpers.createTestWidget(
          Form(
            key: formKey,
            child: TextFormField(
              controller: controller,
              decoration: const InputDecoration(labelText: '邮箱'),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请输入邮箱';
                }
                if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                  return '邮箱格式不正确';
                }
                return null;
              },
            ),
          ),
        );

        await tester.pumpWidget(widget);

        // 测试空值验证
        formKey.currentState!.validate();
        await tester.pump();
        expect(find.text('请输入邮箱'), findsOneWidget);

        // 测试无效邮箱格式
        await tester.enterText(find.byType(TextFormField), 'invalid-email');
        formKey.currentState!.validate();
        await tester.pump();
        expect(find.text('邮箱格式不正确'), findsOneWidget);

        // 测试有效邮箱
        await tester.enterText(find.byType(TextFormField), '<EMAIL>');
        final isValid = formKey.currentState!.validate();
        expect(isValid, isTrue);
      });

      testWidgets('should handle password field', (WidgetTester tester) async {
        bool obscureText = true;

        final widget = TestHelpers.createTestWidget(
          StatefulBuilder(
            builder: (context, setState) {
              return TextFormField(
                obscureText: obscureText,
                decoration: InputDecoration(
                  labelText: '密码',
                  suffixIcon: IconButton(
                    icon: Icon(
                      obscureText ? Icons.visibility : Icons.visibility_off,
                    ),
                    onPressed: () {
                      setState(() {
                        obscureText = !obscureText;
                      });
                    },
                  ),
                ),
              );
            },
          ),
        );

        await tester.pumpWidget(widget);

        // 初始状态应该隐藏密码
        final textField = tester.widget<TextFormField>(find.byType(TextFormField));
        expect(textField.obscureText, isTrue);
        expect(find.byIcon(Icons.visibility), findsOneWidget);

        // 点击显示密码
        await tester.tap(find.byIcon(Icons.visibility));
        await tester.pump();

        expect(find.byIcon(Icons.visibility_off), findsOneWidget);
      });
    });

    group('Custom Card Tests', () {
      testWidgets('should display card with content', (WidgetTester tester) async {
        final widget = TestHelpers.createTestWidget(
          Card(
            elevation: 4,
            margin: const EdgeInsets.all(16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '卡片标题',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text('这是卡片的内容描述'),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () {},
                        child: const Text('取消'),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: () {},
                        child: const Text('确认'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );

        await tester.pumpWidget(widget);

        expect(find.text('卡片标题'), findsOneWidget);
        expect(find.text('这是卡片的内容描述'), findsOneWidget);
        expect(find.text('取消'), findsOneWidget);
        expect(find.text('确认'), findsOneWidget);
        expect(find.byType(Card), findsOneWidget);
      });

      testWidgets('should handle card tap', (WidgetTester tester) async {
        bool wasTapped = false;

        final widget = TestHelpers.createTestWidget(
          GestureDetector(
            onTap: () {
              wasTapped = true;
            },
            child: const Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Text('可点击的卡片'),
              ),
            ),
          ),
        );

        await tester.pumpWidget(widget);

        await tester.tap(find.byType(Card));
        await tester.pump();

        expect(wasTapped, isTrue);
      });
    });

    group('Custom Dialog Tests', () {
      testWidgets('should display alert dialog', (WidgetTester tester) async {
        final widget = TestHelpers.createTestWidget(
          Builder(
            builder: (context) {
              return ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('确认删除'),
                      content: const Text('确定要删除这个项目吗？'),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(false),
                          child: const Text('取消'),
                        ),
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(true),
                          child: const Text('删除'),
                        ),
                      ],
                    ),
                  );
                },
                child: const Text('显示对话框'),
              );
            },
          ),
        );

        await tester.pumpWidget(widget);

        // 点击按钮显示对话框
        await tester.tap(find.text('显示对话框'));
        await tester.pumpAndSettle();

        expect(find.text('确认删除'), findsOneWidget);
        expect(find.text('确定要删除这个项目吗？'), findsOneWidget);
        expect(find.text('取消'), findsOneWidget);
        expect(find.text('删除'), findsOneWidget);

        // 点击取消关闭对话框
        await tester.tap(find.text('取消'));
        await tester.pumpAndSettle();

        expect(find.text('确认删除'), findsNothing);
      });

      testWidgets('should display bottom sheet', (WidgetTester tester) async {
        final widget = TestHelpers.createTestWidget(
          Builder(
            builder: (context) {
              return ElevatedButton(
                onPressed: () {
                  showModalBottomSheet(
                    context: context,
                    builder: (context) => Container(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Text(
                            '选择操作',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          ListTile(
                            leading: const Icon(Icons.edit),
                            title: const Text('编辑'),
                            onTap: () => Navigator.pop(context),
                          ),
                          ListTile(
                            leading: const Icon(Icons.delete),
                            title: const Text('删除'),
                            onTap: () => Navigator.pop(context),
                          ),
                        ],
                      ),
                    ),
                  );
                },
                child: const Text('显示底部弹窗'),
              );
            },
          ),
        );

        await tester.pumpWidget(widget);

        // 点击按钮显示底部弹窗
        await tester.tap(find.text('显示底部弹窗'));
        await tester.pumpAndSettle();

        expect(find.text('选择操作'), findsOneWidget);
        expect(find.text('编辑'), findsOneWidget);
        expect(find.text('删除'), findsOneWidget);
        expect(find.byIcon(Icons.edit), findsOneWidget);
        expect(find.byIcon(Icons.delete), findsOneWidget);
      });
    });

    group('Custom List Tests', () {
      testWidgets('should display list with items', (WidgetTester tester) async {
        final items = ['项目1', '项目2', '项目3'];

        final widget = TestHelpers.createTestWidget(
          ListView.builder(
            itemCount: items.length,
            itemBuilder: (context, index) {
              return ListTile(
                leading: CircleAvatar(child: Text('${index + 1}')),
                title: Text(items[index]),
                subtitle: Text('这是${items[index]}的描述'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {},
              );
            },
          ),
        );

        await tester.pumpWidget(widget);

        expect(find.text('项目1'), findsOneWidget);
        expect(find.text('项目2'), findsOneWidget);
        expect(find.text('项目3'), findsOneWidget);
        expect(find.byType(ListTile), findsNWidgets(3));
        expect(find.byType(CircleAvatar), findsNWidgets(3));
      });

      testWidgets('should handle empty list', (WidgetTester tester) async {
        final items = <String>[];

        final widget = TestHelpers.createTestWidget(
          items.isEmpty
              ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.inbox, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text(
                        '暂无数据',
                        style: TextStyle(color: Colors.grey),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  itemCount: items.length,
                  itemBuilder: (context, index) {
                    return ListTile(title: Text(items[index]));
                  },
                ),
        );

        await tester.pumpWidget(widget);

        expect(find.text('暂无数据'), findsOneWidget);
        expect(find.byIcon(Icons.inbox), findsOneWidget);
        expect(find.byType(ListTile), findsNothing);
      });
    });
  });
}
