import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Mock SharedPreferences for testing
/// 为测试提供模拟的SharedPreferences实现
class MockSharedPreferences {
  static Map<String, dynamic> _storage = {};

  /// 设置测试数据
  static void setMockInitialValues(Map<String, dynamic> values) {
    _storage = Map.from(values);
    SharedPreferences.setMockInitialValues(_storage);
  }

  /// 清空测试数据
  static void clear() {
    _storage.clear();
    SharedPreferences.setMockInitialValues({});
  }

  /// 获取存储的数据
  static Map<String, dynamic> get storage => Map.from(_storage);

  /// 设置字符串值
  static void setString(String key, String value) {
    _storage[key] = value;
    SharedPreferences.setMockInitialValues(_storage);
  }

  /// 设置整数值
  static void setInt(String key, int value) {
    _storage[key] = value;
    SharedPreferences.setMockInitialValues(_storage);
  }

  /// 设置布尔值
  static void setBool(String key, bool value) {
    _storage[key] = value;
    SharedPreferences.setMockInitialValues(_storage);
  }

  /// 设置双精度值
  static void setDouble(String key, double value) {
    _storage[key] = value;
    SharedPreferences.setMockInitialValues(_storage);
  }

  /// 设置字符串列表
  static void setStringList(String key, List<String> value) {
    _storage[key] = value;
    SharedPreferences.setMockInitialValues(_storage);
  }

  /// 移除指定键
  static void remove(String key) {
    _storage.remove(key);
    SharedPreferences.setMockInitialValues(_storage);
  }

  /// 检查是否包含指定键
  static bool containsKey(String key) {
    return _storage.containsKey(key);
  }
}
