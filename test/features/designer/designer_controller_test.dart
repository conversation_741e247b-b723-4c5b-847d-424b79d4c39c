import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_smarthome/core/models/models.dart';
import '../../helpers/test_helpers.dart';
import '../../mocks/mock_shared_preferences.dart';
import '../../mocks/mock_method_channel.dart';

void main() {
  group('Designer Controller Tests', () {
    setUp(() async {
      MockSharedPreferences.clear();
      MockUserMethodChannel.clear();
      MockUserMethodChannel.setUserSyncSuccess();
    });

    tearDown(() {
      MockSharedPreferences.clear();
      MockUserMethodChannel.clear();
    });

    group('Designer Case Tests', () {
      test('should create designer case correctly', () {
        final designerCase = TestHelpers.createTestDesignerCase(
          caseTitle: '现代简约客厅设计',
          caseIntro: '这是一个现代简约风格的客厅设计案例',
          householdType: '三室两厅',
        );

        expect(designerCase.caseTitle, equals('现代简约客厅设计'));
        expect(designerCase.caseIntro, equals('这是一个现代简约风格的客厅设计案例'));
        expect(designerCase.householdType, equals('三室两厅'));
        expect(designerCase.caseMainPic, isNotEmpty);
        expect(designerCase.excelStyle, isNotEmpty);
      });

      test('should filter cases by style', () {
        final cases = [
          DesignerCaseModel(
            id: 'case1',
            caseTitle: '现代简约案例',
            caseMainPic: [],
            excelStyle: ['现代', '简约'],
          ),
          DesignerCaseModel(
            id: 'case2',
            caseTitle: '中式古典案例',
            caseMainPic: [],
            excelStyle: ['中式', '古典'],
          ),
          DesignerCaseModel(
            id: 'case3',
            caseTitle: '北欧风格案例',
            caseMainPic: [],
            excelStyle: ['北欧', '简约'],
          ),
        ];

        // 筛选包含"简约"风格的案例
        final simpleCases = cases.where((case_) {
          return case_.excelStyle.contains('简约');
        }).toList();

        expect(simpleCases, hasLength(2));
        expect(simpleCases[0].id, equals('case1'));
        expect(simpleCases[1].id, equals('case3'));
      });

      test('should sort cases by popularity', () {
        // 由于DesignerCaseModel没有viewCount字段，我们模拟一个包含热度的数据结构
        final casesWithStats = [
          {'case': TestHelpers.createTestDesignerCase(), 'views': 100, 'likes': 10},
          {'case': TestHelpers.createTestDesignerCase(), 'views': 500, 'likes': 50},
          {'case': TestHelpers.createTestDesignerCase(), 'views': 200, 'likes': 20},
        ];

        // 按浏览量排序
        casesWithStats.sort((a, b) => (b['views'] as int).compareTo(a['views'] as int));

        expect(casesWithStats[0]['views'], equals(500));
        expect(casesWithStats[1]['views'], equals(200));
        expect(casesWithStats[2]['views'], equals(100));
      });

      testWidgets('should display case grid correctly', (WidgetTester tester) async {
        final cases = [
          TestHelpers.createTestDesignerCase(caseTitle: '案例1'),
          TestHelpers.createTestDesignerCase(caseTitle: '案例2'),
          TestHelpers.createTestDesignerCase(caseTitle: '案例3'),
        ];

        final widget = TestHelpers.createTestWidget(
          GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 0.8,
            ),
            itemCount: cases.length,
            itemBuilder: (context, index) {
              final case_ = cases[index];
              return Card(
                child: Column(
                  children: [
                    Expanded(
                      child: Container(
                        color: Colors.grey[300],
                        child: const Center(child: Text('图片')),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        case_.caseTitle ?? '',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        );

        await tester.pumpWidget(widget);

        expect(find.text('案例1'), findsOneWidget);
        expect(find.text('案例2'), findsOneWidget);
        expect(find.text('案例3'), findsOneWidget);
        expect(find.byType(Card), findsNWidgets(3));
      });
    });

    group('Designer Profile Tests', () {
      test('should validate designer information', () {
        final designerInfo = {
          'id': 'designer123',
          'name': '张设计师',
          'experience': 5,
          'specialties': ['现代简约', '北欧风格'],
          'rating': 4.8,
          'projects': 50,
          'verified': true,
        };

        expect(designerInfo['name'], equals('张设计师'));
        expect(designerInfo['experience'], equals(5));
        expect(designerInfo['specialties'], contains('现代简约'));
        expect(designerInfo['rating'], greaterThan(4.5));
        expect(designerInfo['verified'], isTrue);
      });

      test('should calculate designer score', () {
        final designers = [
          {
            'name': '设计师A',
            'rating': 4.5,
            'projects': 30,
            'experience': 3,
            'verified': true,
          },
          {
            'name': '设计师B',
            'rating': 4.8,
            'projects': 50,
            'experience': 5,
            'verified': true,
          },
          {
            'name': '设计师C',
            'rating': 4.2,
            'projects': 20,
            'experience': 2,
            'verified': false,
          },
        ];

        // 计算综合评分：评分*0.4 + 项目数/10*0.3 + 经验年数/10*0.2 + 认证加分0.1
        for (final designer in designers) {
          double score = (designer['rating'] as double) * 0.4;
          score += (designer['projects'] as int) / 10 * 0.3;
          score += (designer['experience'] as int) / 10 * 0.2;
          if (designer['verified'] as bool) score += 0.1;
          designer['score'] = score;
        }

        designers.sort((a, b) => (b['score'] as double).compareTo(a['score'] as double));

        expect(designers[0]['name'], equals('设计师B'));
        expect(designers[0]['score'], greaterThan(designers[1]['score'] as double));
      });

      testWidgets('should display designer card', (WidgetTester tester) async {
        final widget = TestHelpers.createTestWidget(
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const CircleAvatar(
                        radius: 30,
                        child: Icon(Icons.person),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              '张设计师',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                Icon(Icons.star, color: Colors.amber, size: 16),
                                const SizedBox(width: 4),
                                const Text('4.8'),
                                const SizedBox(width: 16),
                                const Text('5年经验'),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  const Text('擅长风格：现代简约、北欧风格'),
                  const SizedBox(height: 8),
                  const Text('完成项目：50个'),
                ],
              ),
            ),
          ),
        );

        await tester.pumpWidget(widget);

        expect(find.text('张设计师'), findsOneWidget);
        expect(find.text('4.8'), findsOneWidget);
        expect(find.text('5年经验'), findsOneWidget);
        expect(find.text('擅长风格：现代简约、北欧风格'), findsOneWidget);
        expect(find.text('完成项目：50个'), findsOneWidget);
        expect(find.byIcon(Icons.star), findsOneWidget);
      });
    });

    group('Design Consultation Tests', () {
      test('should validate consultation request', () {
        final consultationRequest = {
          'clientName': '李先生',
          'phone': '13800138000',
          'projectType': '全屋设计',
          'budget': 100000,
          'area': 120.0,
          'style': '现代简约',
          'requirements': '希望设计师能够充分利用空间',
        };

        // 验证必填字段
        expect(consultationRequest['clientName'], isNotEmpty);
        expect(consultationRequest['phone'], matches(r'^1[3-9]\d{9}$'));
        expect(consultationRequest['budget'], greaterThan(0));
        expect(consultationRequest['area'], greaterThan(0));
      });

      test('should match designer with project requirements', () {
        final projectRequirements = {
          'style': '现代简约',
          'budget': 80000,
          'area': 100.0,
        };

        final designers = [
          {
            'name': '设计师A',
            'specialties': ['现代简约', '北欧风格'],
            'minBudget': 50000,
            'maxBudget': 150000,
          },
          {
            'name': '设计师B',
            'specialties': ['中式古典', '欧式古典'],
            'minBudget': 100000,
            'maxBudget': 300000,
          },
          {
            'name': '设计师C',
            'specialties': ['现代简约', '工业风格'],
            'minBudget': 30000,
            'maxBudget': 100000,
          },
        ];

        final matchingDesigners = designers.where((designer) {
          final specialties = designer['specialties'] as List<String>;
          final minBudget = designer['minBudget'] as int;
          final maxBudget = designer['maxBudget'] as int;
          final budget = projectRequirements['budget'] as int;

          return specialties.contains(projectRequirements['style']) &&
                 budget >= minBudget &&
                 budget <= maxBudget;
        }).toList();

        expect(matchingDesigners, hasLength(2));
        expect(matchingDesigners[0]['name'], equals('设计师A'));
        expect(matchingDesigners[1]['name'], equals('设计师C'));
      });

      testWidgets('should display consultation form', (WidgetTester tester) async {
        final widget = TestHelpers.createTestWidget(
          Form(
            child: Column(
              children: [
                TextFormField(
                  decoration: const InputDecoration(labelText: '姓名'),
                  validator: (value) => value?.isEmpty == true ? '请输入姓名' : null,
                ),
                TextFormField(
                  decoration: const InputDecoration(labelText: '手机号'),
                  keyboardType: TextInputType.phone,
                ),
                DropdownButtonFormField<String>(
                  decoration: const InputDecoration(labelText: '项目类型'),
                  items: ['全屋设计', '局部改造', '软装设计']
                      .map((type) => DropdownMenuItem(value: type, child: Text(type)))
                      .toList(),
                  onChanged: (value) {},
                ),
                TextFormField(
                  decoration: const InputDecoration(labelText: '预算（元）'),
                  keyboardType: TextInputType.number,
                ),
                TextFormField(
                  decoration: const InputDecoration(labelText: '面积（平方米）'),
                  keyboardType: TextInputType.number,
                ),
                TextFormField(
                  decoration: const InputDecoration(labelText: '设计需求'),
                  maxLines: 3,
                ),
              ],
            ),
          ),
        );

        await tester.pumpWidget(widget);

        expect(find.text('姓名'), findsOneWidget);
        expect(find.text('手机号'), findsOneWidget);
        expect(find.text('项目类型'), findsOneWidget);
        expect(find.text('预算（元）'), findsOneWidget);
        expect(find.text('面积（平方米）'), findsOneWidget);
        expect(find.text('设计需求'), findsOneWidget);
        expect(find.byType(TextFormField), findsNWidgets(5));
        expect(find.byType(DropdownButtonFormField), findsOneWidget);
      });
    });

    group('Design Portfolio Tests', () {
      test('should organize portfolio by categories', () {
        final portfolioItems = [
          {'title': '现代客厅', 'category': '客厅', 'style': '现代简约'},
          {'title': '温馨卧室', 'category': '卧室', 'style': '北欧风格'},
          {'title': '开放厨房', 'category': '厨房', 'style': '现代简约'},
          {'title': '主卧设计', 'category': '卧室', 'style': '现代简约'},
        ];

        final groupedPortfolio = <String, List<Map<String, String>>>{};
        for (final item in portfolioItems) {
          final category = item['category']!;
          groupedPortfolio.putIfAbsent(category, () => []).add(item);
        }

        expect(groupedPortfolio.keys, hasLength(3));
        expect(groupedPortfolio['客厅'], hasLength(1));
        expect(groupedPortfolio['卧室'], hasLength(2));
        expect(groupedPortfolio['厨房'], hasLength(1));
      });

      test('should filter portfolio by style', () {
        final portfolioItems = [
          {'title': '作品1', 'style': '现代简约'},
          {'title': '作品2', 'style': '北欧风格'},
          {'title': '作品3', 'style': '现代简约'},
          {'title': '作品4', 'style': '中式古典'},
        ];

        final modernWorks = portfolioItems
            .where((item) => item['style'] == '现代简约')
            .toList();

        expect(modernWorks, hasLength(2));
        expect(modernWorks[0]['title'], equals('作品1'));
        expect(modernWorks[1]['title'], equals('作品3'));
      });
    });
  });
}
