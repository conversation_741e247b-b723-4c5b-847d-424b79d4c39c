import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_smarthome/core/models/models.dart';
import '../../helpers/test_helpers.dart';
import '../../mocks/mock_shared_preferences.dart';
import '../../mocks/mock_method_channel.dart';

void main() {
  group('Decoration Controller Tests', () {
    setUp(() async {
      MockSharedPreferences.clear();
      MockUserMethodChannel.clear();
      MockUserMethodChannel.setUserSyncSuccess();
    });

    tearDown(() {
      MockSharedPreferences.clear();
      MockUserMethodChannel.clear();
    });

    group('Address Management Tests', () {
      test('should create address correctly', () {
        final address = TestHelpers.createTestAddress(
          firstName: '张三',
          phoneNumber: '13800138000',
          state: '北京市',
          city: '北京市',
          district: '朝阳区',
          detailedAddress: '某某街道123号',
          isDefault: true,
        );

        expect(address.firstName, equals('张三'));
        expect(address.phoneNumber, equals('13800138000'));
        expect(address.completeAddress, equals('北京市 北京市 朝阳区 某某街道123号'));
        expect(address.isDefault, isTrue);
      });

      test('should validate phone number format', () {
        final validPhones = [
          '13800138000',
          '15912345678',
          '18612345678',
        ];

        final invalidPhones = [
          '1380013800', // 太短
          '138001380001', // 太长
          '12345678901', // 不是手机号格式
          'abc12345678', // 包含字母
        ];

        for (final phone in validPhones) {
          final isValid = RegExp(r'^1[3-9]\d{9}$').hasMatch(phone);
          expect(isValid, isTrue, reason: '$phone should be valid');
        }

        for (final phone in invalidPhones) {
          final isValid = RegExp(r'^1[3-9]\d{9}$').hasMatch(phone);
          expect(isValid, isFalse, reason: '$phone should be invalid');
        }
      });

      test('should handle default address logic', () {
        final addresses = [
          TestHelpers.createTestAddress(id: 'addr1', isDefault: false),
          TestHelpers.createTestAddress(id: 'addr2', isDefault: true),
          TestHelpers.createTestAddress(id: 'addr3', isDefault: false),
        ];

        final defaultAddress = addresses.firstWhere(
          (addr) => addr.isDefault == true,
          orElse: () => addresses.first,
        );

        expect(defaultAddress.id, equals('addr2'));
      });

      testWidgets('should display address form correctly', (WidgetTester tester) async {
        final widget = TestHelpers.createTestWidget(
          Column(
            children: [
              TextFormField(
                decoration: const InputDecoration(labelText: '姓名'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '请输入姓名';
                  }
                  return null;
                },
              ),
              TextFormField(
                decoration: const InputDecoration(labelText: '手机号'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '请输入手机号';
                  }
                  if (!RegExp(r'^1[3-9]\d{9}$').hasMatch(value)) {
                    return '手机号格式不正确';
                  }
                  return null;
                },
              ),
              TextFormField(
                decoration: const InputDecoration(labelText: '详细地址'),
                maxLines: 3,
              ),
            ],
          ),
        );

        await tester.pumpWidget(widget);

        expect(find.text('姓名'), findsOneWidget);
        expect(find.text('手机号'), findsOneWidget);
        expect(find.text('详细地址'), findsOneWidget);
        expect(find.byType(TextFormField), findsNWidgets(3));
      });
    });

    group('Decoration Project Tests', () {
      test('should calculate project progress', () {
        // 模拟装修项目进度
        final projectStages = [
          {'name': '设计阶段', 'completed': true},
          {'name': '材料采购', 'completed': true},
          {'name': '施工阶段', 'completed': false},
          {'name': '验收阶段', 'completed': false},
        ];

        final completedStages = projectStages.where((stage) => stage['completed'] == true).length;
        final totalStages = projectStages.length;
        final progress = (completedStages / totalStages * 100).round();

        expect(progress, equals(50)); // 2/4 = 50%
      });

      test('should validate decoration budget', () {
        final budgetRanges = [
          {'min': 0, 'max': 50000, 'label': '5万以下'},
          {'min': 50000, 'max': 100000, 'label': '5-10万'},
          {'min': 100000, 'max': 200000, 'label': '10-20万'},
          {'min': 200000, 'max': double.infinity, 'label': '20万以上'},
        ];

        final testBudget = 80000;
        final matchingRange = budgetRanges.firstWhere(
          (range) => testBudget >= range['min']! && testBudget < range['max']!,
          orElse: () => budgetRanges.last,
        );

        expect(matchingRange['label'], equals('5-10万'));
      });

      testWidgets('should display project timeline', (WidgetTester tester) async {
        final timelineItems = [
          {'date': '2024-01-01', 'title': '项目启动', 'completed': true},
          {'date': '2024-01-15', 'title': '设计完成', 'completed': true},
          {'date': '2024-02-01', 'title': '施工开始', 'completed': false},
          {'date': '2024-03-01', 'title': '项目完工', 'completed': false},
        ];

        final widget = TestHelpers.createTestWidget(
          ListView.builder(
            itemCount: timelineItems.length,
            itemBuilder: (context, index) {
              final item = timelineItems[index];
              return ListTile(
                leading: Icon(
                  item['completed'] == true ? Icons.check_circle : Icons.radio_button_unchecked,
                  color: item['completed'] == true ? Colors.green : Colors.grey,
                ),
                title: Text(item['title'] as String),
                subtitle: Text(item['date'] as String),
              );
            },
          ),
        );

        await tester.pumpWidget(widget);

        expect(find.text('项目启动'), findsOneWidget);
        expect(find.text('设计完成'), findsOneWidget);
        expect(find.text('施工开始'), findsOneWidget);
        expect(find.text('项目完工'), findsOneWidget);
        expect(find.byIcon(Icons.check_circle), findsNWidgets(2));
        expect(find.byIcon(Icons.radio_button_unchecked), findsNWidgets(2));
      });
    });

    group('Decoration Style Tests', () {
      test('should categorize decoration styles', () {
        final styles = [
          {'name': '现代简约', 'category': '现代'},
          {'name': '北欧风格', 'category': '现代'},
          {'name': '中式古典', 'category': '传统'},
          {'name': '欧式古典', 'category': '传统'},
          {'name': '工业风格', 'category': '特色'},
        ];

        final modernStyles = styles.where((style) => style['category'] == '现代').toList();
        final traditionalStyles = styles.where((style) => style['category'] == '传统').toList();

        expect(modernStyles, hasLength(2));
        expect(traditionalStyles, hasLength(2));
        expect(modernStyles[0]['name'], equals('现代简约'));
        expect(traditionalStyles[0]['name'], equals('中式古典'));
      });

      test('should match style with room type', () {
        final styleRecommendations = {
          '客厅': ['现代简约', '北欧风格', '轻奢风格'],
          '卧室': ['温馨田园', '简约现代', '日式风格'],
          '厨房': ['现代简约', '工业风格', '北欧风格'],
          '卫生间': ['现代简约', '工业风格'],
        };

        final roomType = '客厅';
        final recommendedStyles = styleRecommendations[roomType] ?? [];

        expect(recommendedStyles, hasLength(3));
        expect(recommendedStyles, contains('现代简约'));
        expect(recommendedStyles, contains('北欧风格'));
      });
    });

    group('Material Selection Tests', () {
      test('should calculate material quantities', () {
        final roomArea = 20.0; // 平方米
        final materialRequirements = {
          '地板': {'unit': '平方米', 'ratio': 1.1}, // 考虑损耗
          '墙漆': {'unit': '升', 'ratio': 0.3}, // 每平米需要0.3升
          '瓷砖': {'unit': '片', 'ratio': 4.0}, // 每平米4片
        };

        final materialList = materialRequirements.entries.map((entry) {
          final materialName = entry.key;
          final requirement = entry.value;
          final quantity = (roomArea * (requirement['ratio'] as double)).ceil();
          
          return {
            'name': materialName,
            'quantity': quantity,
            'unit': requirement['unit'],
          };
        }).toList();

        expect(materialList, hasLength(3));
        expect(materialList[0]['name'], equals('地板'));
        expect(materialList[0]['quantity'], equals(22)); // 20 * 1.1 = 22
        expect(materialList[1]['quantity'], equals(6)); // 20 * 0.3 = 6
        expect(materialList[2]['quantity'], equals(80)); // 20 * 4.0 = 80
      });

      test('should validate material budget', () {
        final materials = [
          {'name': '地板', 'price': 200.0, 'quantity': 20},
          {'name': '墙漆', 'price': 50.0, 'quantity': 5},
          {'name': '瓷砖', 'price': 30.0, 'quantity': 80},
        ];

        final totalCost = materials.fold<double>(0.0, (sum, material) {
          return sum + (material['price'] as double) * (material['quantity'] as int);
        });

        expect(totalCost, equals(6650.0)); // (200*20) + (50*5) + (30*80) = 6650
      });
    });

    group('Contractor Management Tests', () {
      test('should rate contractors', () {
        final contractors = [
          {'name': '装修公司A', 'rating': 4.5, 'projects': 50},
          {'name': '装修公司B', 'rating': 4.2, 'projects': 30},
          {'name': '装修公司C', 'rating': 4.8, 'projects': 80},
        ];

        // 按评分排序
        contractors.sort((a, b) => (b['rating'] as double).compareTo(a['rating'] as double));

        expect(contractors[0]['name'], equals('装修公司C'));
        expect(contractors[0]['rating'], equals(4.8));
        expect(contractors[2]['name'], equals('装修公司B'));
      });

      test('should filter contractors by criteria', () {
        final contractors = [
          {'name': '公司A', 'rating': 4.5, 'projects': 50, 'verified': true},
          {'name': '公司B', 'rating': 3.8, 'projects': 20, 'verified': false},
          {'name': '公司C', 'rating': 4.2, 'projects': 35, 'verified': true},
        ];

        // 筛选：评分>=4.0，项目数>=30，已认证
        final qualifiedContractors = contractors.where((contractor) {
          return (contractor['rating'] as double) >= 4.0 &&
                 (contractor['projects'] as int) >= 30 &&
                 (contractor['verified'] as bool) == true;
        }).toList();

        expect(qualifiedContractors, hasLength(2));
        expect(qualifiedContractors[0]['name'], equals('公司A'));
        expect(qualifiedContractors[1]['name'], equals('公司C'));
      });
    });
  });
}
