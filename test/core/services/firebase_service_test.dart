import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/foundation.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter_smarthome/core/services/firebase_service.dart';

// Mock FirebaseCrashlytics
class MockFirebaseCrashlytics extends Fake implements FirebaseCrashlytics {
  bool _isCollectionEnabled = false;
  final List<String> _logs = [];
  final List<Map<String, dynamic>> _errors = [];

  @override
  Future<void> setCrashlyticsCollectionEnabled(bool enabled) async {
    _isCollectionEnabled = enabled;
  }

  @override
  void log(String message) {
    _logs.add(message);
  }

  @override
  Future<void> recordError(
    dynamic exception,
    StackTrace? stackTrace, {
    String? reason,
    Iterable<Object> information = const [],
    bool? printDetails,
    bool fatal = false,
  }) async {
    _errors.add({
      'exception': exception,
      'stackTrace': stackTrace,
      'reason': reason,
      'information': information,
      'printDetails': printDetails,
      'fatal': fatal,
    });
  }

  // 测试辅助方法
  bool get isCollectionEnabled => _isCollectionEnabled;
  List<String> get logs => List.unmodifiable(_logs);
  List<Map<String, dynamic>> get errors => List.unmodifiable(_errors);
  
  void clearLogs() => _logs.clear();
  void clearErrors() => _errors.clear();
}

void main() {
  group('FirebaseService Tests', () {
    late FirebaseService firebaseService;
    late MockFirebaseCrashlytics mockCrashlytics;

    setUp(() {
      mockCrashlytics = MockFirebaseCrashlytics();
      firebaseService = FirebaseService.instance;
      
      // 注入mock实例（这里需要修改FirebaseService以支持依赖注入）
      // 由于原始代码没有依赖注入，我们需要测试公共接口
    });

    tearDown(() {
      mockCrashlytics.clearLogs();
      mockCrashlytics.clearErrors();
    });

    group('Singleton Pattern', () {
      test('should return same instance', () {
        final instance1 = FirebaseService.instance;
        final instance2 = FirebaseService.instance;
        final instance3 = FirebaseService();
        
        expect(instance1, same(instance2));
        expect(instance1, same(instance3));
      });
    });

    group('Initialization', () {
      test('should initialize without throwing exceptions', () async {
        // 由于我们无法直接测试内部的Crashlytics初始化，
        // 我们测试初始化方法不会抛出异常
        expect(
          () => firebaseService.initialize(),
          returnsNormally,
        );
      });
    });

    group('Error Recording', () {
      test('should record error without throwing exceptions', () async {
        final exception = Exception('Test exception');
        final stackTrace = StackTrace.current;
        
        // 测试recordError方法不会抛出异常
        expect(
          () => firebaseService.recordError(
            exception,
            stackTrace,
            reason: 'Test reason',
          ),
          returnsNormally,
        );
      });

      test('should handle null stackTrace', () async {
        final exception = Exception('Test exception');
        
        expect(
          () => firebaseService.recordError(exception, null),
          returnsNormally,
        );
      });

      test('should handle various exception types', () async {
        final exceptions = [
          Exception('Standard exception'),
          ArgumentError('Argument error'),
          StateError('State error'),
          'String error',
          123,
          null,
        ];
        
        for (final exception in exceptions) {
          expect(
            () => firebaseService.recordError(exception, StackTrace.current),
            returnsNormally,
          );
        }
      });
    });

    group('Logging', () {
      test('should log message without throwing exceptions', () {
        final messages = [
          'Simple log message',
          'Log with special characters: !@#\$%^&*()',
          'Log with unicode: 测试日志消息 🚀',
          '',
          'Very long log message: ${'x' * 1000}',
        ];
        
        for (final message in messages) {
          expect(
            () => firebaseService.log(message),
            returnsNormally,
          );
        }
      });
    });

    group('Error Handling', () {
      test('should handle internal errors gracefully', () {
        // 测试当内部操作失败时，服务不会崩溃
        // 这些测试主要验证方法调用的健壮性
        
        expect(
          () => firebaseService.log('test'),
          returnsNormally,
        );
        
        expect(
          () => firebaseService.recordError('error', null),
          returnsNormally,
        );
      });
    });
  });

  group('FirebaseService Integration Tests', () {
    // 这些测试更接近集成测试，验证服务在实际使用场景中的行为
    
    test('should work in typical error reporting scenario', () async {
      final service = FirebaseService.instance;
      
      // 模拟典型的错误报告场景
      try {
        throw Exception('Simulated error for testing');
      } catch (e, stackTrace) {
        // 记录错误应该不会抛出异常
        expect(
          () => service.recordError(e, stackTrace, reason: 'Test scenario'),
          returnsNormally,
        );
        
        // 记录相关日志
        expect(
          () => service.log('Error occurred in test scenario'),
          returnsNormally,
        );
      }
    });

    test('should work in typical logging scenario', () {
      final service = FirebaseService.instance;
      
      // 模拟典型的日志记录场景
      final logMessages = [
        'User logged in',
        'API request started',
        'Data loaded successfully',
        'User performed action: button_click',
        'Session ended',
      ];
      
      for (final message in logMessages) {
        expect(
          () => service.log(message),
          returnsNormally,
        );
      }
    });

    test('should handle rapid successive calls', () {
      final service = FirebaseService.instance;
      
      // 测试快速连续调用的情况
      for (int i = 0; i < 100; i++) {
        expect(
          () => service.log('Rapid log $i'),
          returnsNormally,
        );
        
        if (i % 10 == 0) {
          expect(
            () => service.recordError(
              'Rapid error $i',
              StackTrace.current,
            ),
            returnsNormally,
          );
        }
      }
    });
  });

  group('Debug Mode Behavior', () {
    test('should handle debug mode correctly', () {
      // 在测试环境中，kDebugMode通常为true
      // 验证服务在debug模式下的行为
      
      final service = FirebaseService.instance;
      
      // 初始化应该成功
      expect(
        () => service.initialize(),
        returnsNormally,
      );
      
      // 其他操作也应该正常工作
      expect(
        () => service.log('Debug mode test'),
        returnsNormally,
      );
      
      expect(
        () => service.recordError('Debug error', StackTrace.current),
        returnsNormally,
      );
    });
  });
}
