import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_smarthome/core/utils/navigation_controller.dart';
import '../../mocks/mock_method_channel.dart';

void main() {
  group('NavigationController Tests', () {
    setUp(() {
      // 清除所有模拟数据
      MockNavigationMethodChannel.clear();
    });

    tearDown(() {
      MockNavigationMethodChannel.clear();
    });

    group('Navigation Bar Control', () {
      test('should show navigation bar successfully', () async {
        // 设置成功响应
        MockNavigationMethodChannel.setShowNavigationBarSuccess();
        
        // 调用方法应该不抛出异常
        expect(
          () => NavigationController.showNavigationBar(),
          returnsNormally,
        );
        
        // 验证方法被调用
        expect(
          MockMethodChannel.wasMethodCalled(
            'com.smartlife.navigation',
            'showNavigationBar',
          ),
          isTrue,
        );
      });

      test('should hide navigation bar successfully', () async {
        // 设置成功响应
        MockNavigationMethodChannel.setHideNavigationBarSuccess();
        
        // 调用方法应该不抛出异常
        expect(
          () => NavigationController.hideNavigationBar(),
          returnsNormally,
        );
        
        // 验证方法被调用
        expect(
          MockMethodChannel.wasMethodCalled(
            'com.smartlife.navigation',
            'hideNavigationBar',
          ),
          isTrue,
        );
      });

      test('should handle platform exception when showing navigation bar', () async {
        // 设置抛出异常
        MockMethodChannel.setMockMethodCallException(
          'com.smartlife.navigation',
          'showNavigationBar',
          PlatformException(code: 'ERROR', message: 'Failed to show navigation bar'),
        );
        
        // 方法应该处理异常而不崩溃
        expect(
          () => NavigationController.showNavigationBar(),
          returnsNormally,
        );
      });

      test('should handle platform exception when hiding navigation bar', () async {
        // 设置抛出异常
        MockMethodChannel.setMockMethodCallException(
          'com.smartlife.navigation',
          'hideNavigationBar',
          PlatformException(code: 'ERROR', message: 'Failed to hide navigation bar'),
        );
        
        // 方法应该处理异常而不崩溃
        expect(
          () => NavigationController.hideNavigationBar(),
          returnsNormally,
        );
      });
    });

    group('Navigation Handler Setup', () {
      test('should setup navigation handler successfully', () async {
        expect(
          () => NavigationController.setupNavigationHandler(),
          returnsNormally,
        );
      });
    });

    group('Navigator Key', () {
      test('should provide global navigator key', () {
        final navigatorKey = NavigationController.navigatorKey;
        
        expect(navigatorKey, isNotNull);
        expect(navigatorKey, isA<GlobalKey<NavigatorState>>());
      });

      test('should return same navigator key instance', () {
        final key1 = NavigationController.navigatorKey;
        final key2 = NavigationController.navigatorKey;
        
        expect(key1, same(key2));
      });
    });

    group('Navigation Handling', () {
      testWidgets('should handle showProjectList navigation', (WidgetTester tester) async {
        // 创建测试应用
        await tester.pumpWidget(
          MaterialApp(
            navigatorKey: NavigationController.navigatorKey,
            home: const Scaffold(
              body: Text('Home'),
            ),
          ),
        );
        
        // 模拟导航调用
        final methodCall = MethodCall('showProjectList');
        
        // 调用导航处理器
        expect(
          () => NavigationController.handleNavigation(methodCall),
          returnsNormally,
        );
      });

      testWidgets('should handle navigation when context is available', (WidgetTester tester) async {
        // 创建测试应用
        await tester.pumpWidget(
          MaterialApp(
            navigatorKey: NavigationController.navigatorKey,
            home: const Scaffold(
              body: Text('Home'),
            ),
          ),
        );
        
        // 确保context可用
        expect(NavigationController.navigatorKey.currentContext, isNotNull);
        
        // 模拟导航调用
        final methodCall = MethodCall('showProjectList');
        
        expect(
          () => NavigationController.handleNavigation(methodCall),
          returnsNormally,
        );
      });

      test('should handle navigation when context is null', () async {
        // 当没有设置navigator key的context时
        final methodCall = MethodCall('showProjectList');
        
        expect(
          () => NavigationController.handleNavigation(methodCall),
          returnsNormally,
        );
      });

      test('should handle unknown navigation methods', () async {
        final methodCall = MethodCall('unknownMethod');
        
        expect(
          () => NavigationController.handleNavigation(methodCall),
          returnsNormally,
        );
      });
    });

    group('Method Channel Integration', () {
      test('should use correct method channel name', () {
        // 验证使用了正确的方法通道名称
        expect(
          () => NavigationController.showNavigationBar(),
          returnsNormally,
        );
        
        // 检查是否调用了正确的通道
        final callHistory = MockMethodChannel.getCallHistory('com.smartlife.navigation');
        expect(callHistory, isNotEmpty);
      });

      test('should handle multiple method calls', () async {
        MockNavigationMethodChannel.setShowNavigationBarSuccess();
        MockNavigationMethodChannel.setHideNavigationBarSuccess();
        
        // 执行多个方法调用
        await NavigationController.showNavigationBar();
        await NavigationController.hideNavigationBar();
        await NavigationController.showNavigationBar();
        
        // 验证所有调用都被记录
        expect(
          MockMethodChannel.getMethodCallCount('com.smartlife.navigation', 'showNavigationBar'),
          equals(2),
        );
        expect(
          MockMethodChannel.getMethodCallCount('com.smartlife.navigation', 'hideNavigationBar'),
          equals(1),
        );
      });
    });

    group('Error Scenarios', () {
      test('should handle method channel communication errors', () async {
        // 设置通道错误
        MockMethodChannel.setMockMethodCallException(
          'com.smartlife.navigation',
          'showNavigationBar',
          PlatformException(
            code: 'CHANNEL_ERROR',
            message: 'Method channel communication failed',
          ),
        );
        
        expect(
          () => NavigationController.showNavigationBar(),
          returnsNormally,
        );
      });

      test('should handle timeout errors', () async {
        // 模拟超时错误
        MockMethodChannel.setMockMethodCallException(
          'com.smartlife.navigation',
          'hideNavigationBar',
          PlatformException(
            code: 'TIMEOUT',
            message: 'Operation timed out',
          ),
        );
        
        expect(
          () => NavigationController.hideNavigationBar(),
          returnsNormally,
        );
      });
    });
  });

  group('NavigationController Integration Tests', () {
    testWidgets('should work in complete navigation scenario', (WidgetTester tester) async {
      // 设置完整的导航场景
      MockNavigationMethodChannel.setShowNavigationBarSuccess();
      MockNavigationMethodChannel.setHideNavigationBarSuccess();
      
      // 创建应用
      await tester.pumpWidget(
        MaterialApp(
          navigatorKey: NavigationController.navigatorKey,
          home: const Scaffold(
            body: Text('Test App'),
          ),
        ),
      );
      
      // 执行导航操作序列
      await NavigationController.showNavigationBar();
      await NavigationController.hideNavigationBar();
      
      // 处理导航事件
      final methodCall = MethodCall('showProjectList');
      await NavigationController.handleNavigation(methodCall);
      
      // 验证应用仍然正常运行
      expect(find.text('Test App'), findsOneWidget);
    });

    testWidgets('should maintain navigator key across operations', (WidgetTester tester) async {
      // 创建应用
      await tester.pumpWidget(
        MaterialApp(
          navigatorKey: NavigationController.navigatorKey,
          home: const Scaffold(
            body: Text('Test'),
          ),
        ),
      );
      
      // 获取初始的navigator key
      final initialKey = NavigationController.navigatorKey;
      
      // 执行一些操作
      await NavigationController.showNavigationBar();
      await NavigationController.hideNavigationBar();
      
      // 验证navigator key保持不变
      expect(NavigationController.navigatorKey, same(initialKey));
      expect(NavigationController.navigatorKey.currentContext, isNotNull);
    });
  });
}
