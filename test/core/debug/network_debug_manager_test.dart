import 'package:flutter_test/flutter_test.dart';
import 'package:dio/dio.dart';
import 'package:flutter_smarthome/core/debug/network_debug_manager.dart';

void main() {
  group('NetworkDebugManager Tests', () {
    late NetworkDebugManager debugManager;

    setUp(() {
      debugManager = NetworkDebugManager();
      debugManager.clear(); // 确保每个测试开始时都是干净的状态
    });

    tearDown(() {
      debugManager.clear();
    });

    group('Singleton Pattern', () {
      test('should return same instance', () {
        final instance1 = NetworkDebugManager();
        final instance2 = NetworkDebugManager();
        
        expect(instance1, same(instance2));
      });
    });

    group('Request Logging', () {
      test('should log request successfully', () {
        final options = RequestOptions(
          path: '/test',
          method: 'GET',
          baseUrl: 'https://api.example.com',
        );
        
        final requestId = debugManager.logRequest(options);
        
        expect(requestId, isNotEmpty);
        expect(debugManager.requests, hasLength(1));
        
        final request = debugManager.requests.first;
        expect(request.id, equals(requestId));
        expect(request.method, equals('GET'));
        expect(request.url, equals('https://api.example.com/test'));
      });

      test('should generate unique request IDs', () {
        final options1 = RequestOptions(path: '/test1', method: 'GET');
        final options2 = RequestOptions(path: '/test2', method: 'POST');
        
        final id1 = debugManager.logRequest(options1);
        final id2 = debugManager.logRequest(options2);
        
        expect(id1, isNot(equals(id2)));
        expect(debugManager.requests, hasLength(2));
      });

      test('should store request data correctly', () {
        final requestData = {'key': 'value', 'number': 123};
        final headers = {'Authorization': 'Bearer token', 'Content-Type': 'application/json'};
        
        final options = RequestOptions(
          path: '/test',
          method: 'POST',
          data: requestData,
          headers: headers,
        );
        
        debugManager.logRequest(options);
        
        final request = debugManager.requests.first;
        expect(request.requestData, equals(requestData));
        expect(request.headers, equals(headers));
      });
    });

    group('Response Logging', () {
      test('should log successful response', () {
        final options = RequestOptions(path: '/test', method: 'GET');
        final requestId = debugManager.logRequest(options);
        
        final responseData = {'result': 'success'};
        final response = Response(
          data: responseData,
          statusCode: 200,
          requestOptions: options,
        );
        
        final duration = Duration(milliseconds: 500);
        debugManager.logResponse(requestId, response, duration);
        
        final request = debugManager.requests.first;
        expect(request.statusCode, equals(200));
        expect(request.responseData, equals(responseData));
        expect(request.duration, equals(duration));
        expect(request.isSuccess, isTrue);
        expect(request.isError, isFalse);
      });

      test('should handle response for non-existent request', () {
        final options = RequestOptions(path: '/test', method: 'GET');
        final response = Response(
          data: {'result': 'success'},
          statusCode: 200,
          requestOptions: options,
        );
        
        // 尝试为不存在的请求记录响应
        expect(
          () => debugManager.logResponse('non-existent-id', response, Duration(milliseconds: 100)),
          returnsNormally,
        );
        
        expect(debugManager.requests, isEmpty);
      });
    });

    group('Error Logging', () {
      test('should log error response', () {
        final options = RequestOptions(path: '/test', method: 'GET');
        final requestId = debugManager.logRequest(options);
        
        final errorResponse = Response(
          data: {'error': 'Not found'},
          statusCode: 404,
          requestOptions: options,
        );
        
        final error = DioException(
          requestOptions: options,
          response: errorResponse,
          type: DioExceptionType.badResponse,
          error: 'Not found',
        );
        
        final duration = Duration(milliseconds: 300);
        debugManager.logError(requestId, error, duration);
        
        final request = debugManager.requests.first;
        expect(request.statusCode, equals(404));
        expect(request.responseData, equals({'error': 'Not found'}));
        expect(request.error, equals('Not found'));
        expect(request.duration, equals(duration));
        expect(request.isError, isTrue);
        expect(request.isSuccess, isFalse);
      });

      test('should handle error without response', () {
        final options = RequestOptions(path: '/test', method: 'GET');
        final requestId = debugManager.logRequest(options);
        
        final error = DioException(
          requestOptions: options,
          type: DioExceptionType.connectionTimeout,
          error: 'Connection timeout',
        );
        
        debugManager.logError(requestId, error, Duration(milliseconds: 5000));
        
        final request = debugManager.requests.first;
        expect(request.statusCode, isNull);
        expect(request.responseData, isNull);
        expect(request.error, equals('Connection timeout'));
        expect(request.isError, isTrue);
      });
    });

    group('Request Management', () {
      test('should maintain maximum request limit', () {
        // 添加超过最大限制的请求
        for (int i = 0; i < 150; i++) {
          final options = RequestOptions(path: '/test$i', method: 'GET');
          debugManager.logRequest(options);
        }
        
        // 应该只保留最新的100个请求
        expect(debugManager.requests, hasLength(100));
        
        // 最新的请求应该在前面
        expect(debugManager.requests.first.url, contains('/test149'));
        expect(debugManager.requests.last.url, contains('/test50'));
      });

      test('should clear all requests', () {
        // 添加一些请求
        for (int i = 0; i < 5; i++) {
          final options = RequestOptions(path: '/test$i', method: 'GET');
          debugManager.logRequest(options);
        }
        
        expect(debugManager.requests, hasLength(5));
        
        debugManager.clear();
        
        expect(debugManager.requests, isEmpty);
      });

      test('should order requests by newest first', () {
        final options1 = RequestOptions(path: '/first', method: 'GET');
        final options2 = RequestOptions(path: '/second', method: 'GET');
        final options3 = RequestOptions(path: '/third', method: 'GET');
        
        debugManager.logRequest(options1);
        debugManager.logRequest(options2);
        debugManager.logRequest(options3);
        
        expect(debugManager.requests, hasLength(3));
        expect(debugManager.requests[0].url, contains('/third'));
        expect(debugManager.requests[1].url, contains('/second'));
        expect(debugManager.requests[2].url, contains('/first'));
      });
    });

    group('Statistics', () {
      test('should calculate statistics correctly', () {
        // 添加不同状态的请求
        final options1 = RequestOptions(path: '/success', method: 'GET');
        final options2 = RequestOptions(path: '/error', method: 'GET');
        final options3 = RequestOptions(path: '/pending', method: 'GET');
        
        final id1 = debugManager.logRequest(options1);
        final id2 = debugManager.logRequest(options2);
        final id3 = debugManager.logRequest(options3);
        
        // 模拟成功响应
        final successResponse = Response(
          data: {'result': 'success'},
          statusCode: 200,
          requestOptions: options1,
        );
        debugManager.logResponse(id1, successResponse, Duration(milliseconds: 100));
        
        // 模拟错误响应
        final error = DioException(
          requestOptions: options2,
          type: DioExceptionType.badResponse,
          error: 'Error',
        );
        debugManager.logError(id2, error, Duration(milliseconds: 200));
        
        // id3保持pending状态
        
        final stats = debugManager.getStatistics();
        
        expect(stats['total'], equals(3));
        expect(stats['success'], equals(1));
        expect(stats['error'], equals(1));
        expect(stats['pending'], equals(1));
      });

      test('should handle empty request list', () {
        final stats = debugManager.getStatistics();
        
        expect(stats['total'], equals(0));
        expect(stats['success'], equals(0));
        expect(stats['error'], equals(0));
        expect(stats['pending'], equals(0));
      });
    });

    group('Listeners', () {
      test('should notify listeners when request is added', () {
        bool notified = false;
        debugManager.addListener(() {
          notified = true;
        });
        
        final options = RequestOptions(path: '/test', method: 'GET');
        debugManager.logRequest(options);
        
        expect(notified, isTrue);
      });

      test('should notify listeners when response is logged', () {
        final options = RequestOptions(path: '/test', method: 'GET');
        final requestId = debugManager.logRequest(options);
        
        bool notified = false;
        debugManager.addListener(() {
          notified = true;
        });
        
        final response = Response(
          data: {'result': 'success'},
          statusCode: 200,
          requestOptions: options,
        );
        debugManager.logResponse(requestId, response, Duration(milliseconds: 100));
        
        expect(notified, isTrue);
      });

      test('should remove listeners correctly', () {
        bool notified = false;
        void listener() {
          notified = true;
        }
        
        debugManager.addListener(listener);
        debugManager.removeListener(listener);
        
        final options = RequestOptions(path: '/test', method: 'GET');
        debugManager.logRequest(options);
        
        expect(notified, isFalse);
      });

      test('should notify listeners when cleared', () {
        bool notified = false;
        debugManager.addListener(() {
          notified = true;
        });
        
        debugManager.clear();
        
        expect(notified, isTrue);
      });
    });
  });
}
