name: flutter_smarthome
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.1.0 <4.0.0'
  #sdk: '>=2.19.6 <3.0.0'
  
# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8   #图标库
  dio: ^5.8.0+1 #网络请求库
  flutter_screenutil: ^5.9.3 #屏幕适配库
  oktoast: ^3.4.0 #toast库
  pull_to_refresh: ^2.0.0 #下拉刷新库
  provider: ^6.1.4 #状态管理库
  flashy_tab_bar2: ^0.0.10 #底部导航栏库
  contained_tab_bar_view: ^0.8.0 #SegmentedControl库
  card_swiper: ^3.0.1 #Banner库
  gif_view: ^1.0.2 #gif库
  flutter_infinite_marquee: ^1.0.0 #跑马灯库
  cached_network_image: ^3.4.1 #图片缓存库
  flutter_easyloading: ^3.0.5 #加载loading库
  shared_preferences: 2.5.3 #本地存储库
  city_pickers: ^1.3.0 #城市选择器库
  image_picker: ^1.1.2 #图片选择器库
  webview_flutter: ^4.11.0 #webview库
  webview_flutter_android: ^4.8.0 #Android WebView库
  webview_flutter_wkwebview: ^3.15.0 #iOS WebView库
  package_info_plus: ^8.3.0 #获取app信息库
  connectivity_plus: ^6.1.3 
  video_player: ^2.9.5 #播放器库
  fluwx: ^5.5.0 #微信支付库
  permission_handler: ^12.0.0+1 #权限申请库
  web_socket_channel: ^3.0.1 #WebSocket库
  qr_flutter: ^4.1.0 #二维码生成库
  screenshot: ^3.0.0 #截屏库
  path_provider: ^2.1.4 #路径提供库
  syncfusion_flutter_pdfviewer: ^30.1.40 #PDF查看器库
  http: ^1.2.2 #HTTP请求库（PDF下载用）

  # Firebase 相关依赖
  firebase_core: ^3.8.0 #Firebase 核心库
  firebase_crashlytics: ^4.1.3 #Firebase 崩溃报告库

  flutter_localizations:
    sdk: flutter


dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true
  assets:
    - assets/images/common/
    - assets/images/tabbar/
    - assets/images/decoration/
    - assets/images/shopping/
    - assets/images/personal/
    - assets/images/designer/
    - assets/images/signature/
    - assets/images/2.0x/
    - assets/images/3.0x/
    
